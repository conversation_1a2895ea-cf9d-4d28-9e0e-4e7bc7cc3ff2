{"compilerOptions": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022"], "module": "esnext", "moduleResolution": "bundler", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": "es2022", "customConditions": ["development"]}}