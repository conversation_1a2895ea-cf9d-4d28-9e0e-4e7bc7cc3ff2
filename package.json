{"name": "@nurse-platform/source", "version": "0.0.0", "license": "MIT", "scripts": {"dev:frontend": "nx dev frontend", "dev:backend": "nx serve backend", "dev": "nx run-many --target=dev,serve --projects=frontend,backend --parallel=2", "build": "nx run-many --target=build --projects=frontend,backend", "start": "nx run-many --target=start,serve --projects=frontend,backend --parallel=2 --prod"}, "private": true, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.0", "@nestjs/swagger": "^11.2.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^17.2.0", "framer-motion": "^12.23.0", "handlebars": "^4.7.8", "lucide-react": "^0.525.0", "mongoose": "^8.16.0", "next": "~15.2.4", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "react": "19.0.0", "react-dom": "19.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@nx/js": "21.2.1", "@nx/nest": "^21.2.1", "@nx/next": "21.2.1", "@nx/node": "21.2.1", "@nx/web": "21.2.1", "@nx/webpack": "21.2.1", "@nx/workspace": "21.2.1", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/bcryptjs": "^2.4.6", "@types/node": "~18.16.9", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "autoprefixer": "10.4.13", "nx": "21.2.1", "postcss": "8.4.38", "tailwindcss": "3.4.3", "tslib": "^2.3.0", "typescript": "~5.8.2", "webpack-cli": "^5.1.4"}, "workspaces": ["apps/*", "frontend", "backend", "backend-e2e"]}