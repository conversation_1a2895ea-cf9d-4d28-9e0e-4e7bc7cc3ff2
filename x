[33m83d045f[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m)[m Remove .env file from tracking
[33m43feafc[m update env
[33mdd49558[m update in app adding logo and update in some features
[33m0d46035[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m Merge pull request #33 from Ghada-mahmoud3/main
[33mebbefe7[m edit
[33m9769ae1[m edit
[33mb5f913e[m Merge pull request #32 from mahagallall/main
[33m74ac9a6[m update patient and nurse features
[33m3700e60[m Merge pull request #31 from mostafa99hosny/main
[33m9da7512[m updating in apps
[33ma4d64e4[m Merge pull request #30 from mahagallall/main
[33m73bae96[m update apps
[33mbe4a58b[m Merge pull request #29 from mahagallall/main
[33mfc48c4a[m updating in project
[33mf7078d8[m Merge pull request #28 from HamdySalah/chatBot
[33ma5de08a[m add cat bot in app
[33ma817dea[m Merge pull request #27 from mahagallall/main
[33m8b168f8[m update in nurse role
[33m5360596[m Merge pull request #26 from mostafa99hosny/main
[33m834cc40[m updte in nurse role adding forms to complete profile
[33m79c0d4f[m Merge pull request #25 from mostafa99hosny/main
[33m796a8de[m update frontend and backend
[33m4179666[m Merge pull request #24 from mostafa99hosny/main
[33md518823[m Merge branch 'main' into main
[33m3bc54cc[m updated
[33m168d068[m final add massage gmail
[33md93bbe0[m add massage
[33m9f9b679[m Merge branch 'main' of https://github.com/HamdySalah/Graduation-Project-ITI
[33m43bb03b[m update .env
[33m5ba93ee[m added schemas
[33m845ca83[m added review endpoints
[33mdc8fbac[m send massage
[33mc2eefbc[m Merge pull request #21 from mostafa99hosny/main
[33mbcc3733[m Fix errors in retrieving data from the backend
[33m4fb0b3d[m Merge branch 'main' of github.com:HamdySalah/Graduation-Project-ITI
[33m53e7da4[m added pages
[33m9cc0a09[m Merge pull request #19 from mostafa99hosny/main
[33m2a4a87b[m Merge branch 'main' of https://github.com/mostafa99hosny/Graduation-Project-ITI
[33md1681ba[m update in frontend
[33md70b8a9[m Merge pull request #18 from mostafa99hosny/main
[33m54c61ac[m added dashboard api
[33mf782c72[m update frontend
[33mea45ca0[m update backend in auth behaviour
[33m5d68cbd[m Merge pull request #16 from mostafa99hosny/main
[33me9e7dcd[m update front-end and add features in admin dashboard
[33ma98cf0e[m Merge pull request #15 from mostafa99hosny/main
[33m6232d42[m update in backend
[33m9ef961f[m Merge pull request #14 from mostafa99hosny/main
[33mda916a9[m update in frontend adding pages belong to nurses and patients and adding layout in component and adding lib directory
[33m87c7f2c[m update in backend nurses service
[33m4d1b73c[m Merge pull request #13 from HamdySalah/backendEdit
[33m53aec8f[m Update backend api
[33m90d1f66[m Merge pull request #12 from mostafa99hosny/main
[33m1c7e05b[m update login and register pages
[33md55ebf3[m Merge branch 'main' of github.com:HamdySalah/Graduation-Project-ITI
[33m7fc385e[m test
[33m0c16812[m Merge pull request #11 from mostafa99hosny/updateFront
[33md72c7e1[m[33m ([m[1;31morigin/updateFront[m[33m, [m[1;32mupdateFront[m[33m)[m navbar and hero and testimonial components
[33m2625e34[m Merge pull request #10 from mahagallall/main
[33m69a78e1[m update CallToAction , ContactUs and Footer components
[33mae21aa9[m Merge pull request #9 from mostafa99hosny/updateFront
[33m1c092bd[m update indes.tsx
[33maf5108a[m Merge pull request #8 from mostafa99hosny/updateFront
[33mb573d91[m update components
[33m460765e[m Merge pull request #7 from mahagallall/mycomponents
[33mdb6c996[m adding Footer component
[33m5e9e8f7[m Merge pull request #6 from mahagallall/mycomponents
[33m7ce1350[m adding ContactUs component
[33me123e78[m adding CallToAction component
[33m9e50070[m Merge pull request #5 from mostafa99hosny/main
[33me8d245b[m resolve src conflect
[33m85f6339[m added new Endpoints
[33me8e1644[m merge front
[33md0a24ea[m Merge pull request #3 from Ghada-mahmoud3/feat/home-add-component
[33me0f2e98[m adding pages login and register and index to render home and adding components navbar and herosection and testimoniationSection
[33m004df88[m Add new component to Home page
[33m42b8ef2[m updated api test file
[33m025092f[m updated request services
[33ma388cbc[m updated request services
[33med6357a[m updated schema
[33m397ecbc[m added UI/UX Auth Pages
[33m2ff94cd[m repush endpoints
[33m3b81d20[m added UI/UX Admin Pages on Repo
[33m1c5ffa8[m reintit commit
[33mbecf20a[m Merge branch 'main' of github.com:HamdySalah/Graduation-Project-ITI
[33m000810b[m first commit
[33m64a0b82[m first commit
[33mc528a8d[m Merge branch 'main' of github.com:HamdySalah/Graduation-Project-ITI
[33m52dd54c[m first commit
[33m118e03e[m Initial commit
