const fs = require('fs');
const path = require('path');

// Common unused imports and variables to remove
const unusedImports = [
  'Card',
  'Image',
  'StatusBadge', 
  'SessionStatus',
  'apiService',
  'error',
  'setError',
  'successMessage',
  'setSuccessMessage',
  'loadingStats',
  'setLoadingStats',
  'allUsers',
  'setAllUsers',
  'getCompletionStatus'
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Remove unused imports
    unusedImports.forEach(importName => {
      // Remove from destructured imports
      const destructuredRegex = new RegExp(`import\\s+[^{]*{\\s*([^}]*)\\b${importName}\\b([^}]*)\\s*}\\s+from`, 'g');
      content = content.replace(destructuredRegex, (match, before, after) => {
        const parts = before.split(',').concat(after.split(',')).filter(part => 
          part.trim() && !part.trim().includes(importName)
        );
        if (parts.length > 0) {
          return `import { ${parts.join(', ')} } from`;
        } else {
          return 'import';
        }
      });

      // Remove standalone imports
      const standaloneRegex = new RegExp(`import\\s+${importName}\\s+from[^;]+;?\\s*`, 'g');
      content = content.replace(standaloneRegex, '');
    });

    // Remove unused variable declarations
    unusedImports.forEach(varName => {
      const varRegex = new RegExp(`const\\s+\\[${varName},\\s*set${varName.charAt(0).toUpperCase() + varName.slice(1)}\\]\\s*=\\s*useState[^;]+;?\\s*`, 'g');
      content = content.replace(varRegex, '');
    });

    if (content !== fs.readFileSync(filePath, 'utf8')) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
      modified = true;
    }

    return modified;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Process all TypeScript/JavaScript files
function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      processDirectory(filePath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fixFile(filePath);
    }
  });
}

// Start processing
console.log('Fixing unused imports and variables...');
processDirectory('./apps/frontend');
console.log('Done!'); 