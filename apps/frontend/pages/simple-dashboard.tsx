import { useAuth } from '../lib/auth';
import Link from 'next/link';
import { useState, useRef, useEffect } from 'react';

export default function SimpleDashboard() {
  const { user, logout } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const closeDropdown = () => {
    setIsDropdownOpen(false);
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold mb-4">Please Login</h1>
          <Link href="/dev-auth" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Simple Navbar */}
      <nav className="bg-blue-600 text-white p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div>
            <Link href="/" className="text-xl font-bold hover:text-blue-200">
              Healthcare App
            </Link>
          </div>
          
          {/* User Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button 
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-800 px-3 py-2 rounded transition-colors"
            >
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">{user.name.charAt(0).toUpperCase()}</span>
              </div>
              <span className="font-medium">{user.name}</span>
              <svg 
                className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            {isDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="py-1">
                  <Link 
                    href="/profile" 
                    className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                    onClick={closeDropdown}
                  >
                    📄 Profile
                  </Link>
                  
                  <Link 
                    href="/dashboard" 
                    className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                    onClick={closeDropdown}
                  >
                    📊 Dashboard
                  </Link>
                  
                  {user.role === 'patient' && (
                    <>
                      <Link 
                        href="/requests" 
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={closeDropdown}
                      >
                        📋 My Requests
                      </Link>
                      
                      <Link 
                        href="/requests/create" 
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={closeDropdown}
                      >
                        ➕ Create Request
                      </Link>
                      
                      <Link 
                        href="/nurses" 
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={closeDropdown}
                      >
                        👩‍⚕️ Find Nurses
                      </Link>
                    </>
                  )}
                  
                  {user.role === 'nurse' && (
                    <Link 
                      href="/requests" 
                      className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={closeDropdown}
                    >
                      📋 My Requests
                    </Link>
                  )}
                  
                  <div className="border-t border-gray-100 my-1"></div>
                  
                  <button
                    onClick={() => {
                      closeDropdown();
                      logout();
                    }}
                    className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 transition-colors"
                  >
                    🚪 Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Simple Dashboard Test</h1>
          
          {/* User Info */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">User Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
              <div>
                <p><strong>Name:</strong> {user.name}</p>
                <p><strong>Email:</strong> {user.email}</p>
              </div>
              <div>
                <p><strong>Role:</strong> {user.role}</p>
                <p><strong>Status:</strong> {user.status}</p>
              </div>
              <div>
                <p><strong>Navigation:</strong> Click dropdown above</p>
                <p><strong>Test:</strong> Try clicking Profile or other links</p>
              </div>
            </div>
          </div>

          {/* Navigation Test Buttons */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Direct Navigation Test</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Link 
                href="/profile" 
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors text-center"
              >
                Go to Profile
              </Link>
              
              <Link 
                href="/dashboard" 
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors text-center"
              >
                Go to Dashboard
              </Link>
              
              <Link 
                href="/" 
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors text-center"
              >
                Go to Home
              </Link>
              
              <Link 
                href="/navigation-test" 
                className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors text-center"
              >
                Navigation Test
              </Link>
            </div>
          </div>

          {/* JavaScript Navigation Test */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">JavaScript Navigation Test</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <button 
                onClick={() => window.location.href = '/profile'}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                JS: Profile
              </button>
              
              <button 
                onClick={() => window.location.href = '/dashboard'}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
              >
                JS: Dashboard
              </button>
              
              <button 
                onClick={() => window.location.href = '/'}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
              >
                JS: Home
              </button>
              
              <button 
                onClick={() => {
                  console.log('Testing navigation...');
                  alert('Navigation test - check console');
                  window.location.href = '/profile';
                }}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
              >
                Debug Navigation
              </button>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-semibold text-yellow-900 mb-2">🧪 Navigation Test Instructions</h3>
            <ol className="text-sm text-yellow-700 space-y-1">
              <li>1. Try clicking the user dropdown in the navbar above</li>
              <li>2. Click on "Profile" or "Dashboard" links in the dropdown</li>
              <li>3. Try the direct navigation buttons below</li>
              <li>4. Try the JavaScript navigation buttons</li>
              <li>5. Check browser console for any errors</li>
              <li>6. If navigation fails, note which method fails</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
