import { useRouter } from 'next/router';
import { useAuth } from '../lib/auth';
import { useState, useEffect } from 'react';

export default function MinimalTest() {
  const router = useRouter();
  const { user } = useAuth();
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setLogs(prev => [...prev, logEntry]);
  };

  useEffect(() => {
    addLog('MinimalTest component mounted');
    addLog(`Current route: ${router.pathname}`);
    addLog(`Router ready: ${router.isReady}`);
    addLog(`User: ${user?.email || 'Not logged in'}`);
  }, []);

  const testNavigation = async (url: string) => {
    addLog(`Testing navigation to: ${url}`);
    
    try {
      addLog('Calling router.push...');
      const result = await router.push(url);
      addLog(`Router.push result: ${JSON.stringify(result)}`);
      addLog('Navigation completed successfully');
    } catch (error) {
      addLog(`Navigation failed: ${error}`);
      addLog('Trying window.location fallback...');
      window.location.href = url;
    }
  };

  const forceReload = () => {
    addLog('Force reloading page...');
    window.location.reload();
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      padding: '20px', 
      fontFamily: 'monospace',
      backgroundColor: '#f0f0f0'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>🔍 Minimal Navigation Test</h1>
      
      {/* Current Status */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        marginBottom: '20px', 
        border: '1px solid #ddd',
        borderRadius: '5px'
      }}>
        <h2>Current Status:</h2>
        <p><strong>Route:</strong> {router.pathname}</p>
        <p><strong>URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
        <p><strong>User:</strong> {user?.email || 'Not logged in'}</p>
        <p><strong>Router Ready:</strong> {router.isReady ? 'Yes' : 'No'}</p>
      </div>

      {/* Navigation Tests */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        marginBottom: '20px', 
        border: '1px solid #ddd',
        borderRadius: '5px'
      }}>
        <h2>Navigation Tests:</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '10px' }}>
          <button 
            onClick={() => testNavigation('/profile')}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#007bff', 
              color: 'white', 
              border: 'none', 
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Go to Profile
          </button>
          
          <button 
            onClick={() => testNavigation('/dashboard')}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#28a745', 
              color: 'white', 
              border: 'none', 
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Go to Dashboard
          </button>
          
          <button 
            onClick={() => testNavigation('/')}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#6f42c1', 
              color: 'white', 
              border: 'none', 
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Go to Home
          </button>
          
          <button 
            onClick={() => testNavigation('/login')}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#fd7e14', 
              color: 'white', 
              border: 'none', 
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Go to Login
          </button>
        </div>
        
        <button 
          onClick={forceReload}
          style={{ 
            padding: '10px 15px', 
            backgroundColor: '#dc3545', 
            color: 'white', 
            border: 'none', 
            borderRadius: '3px',
            cursor: 'pointer'
          }}
        >
          Force Reload Page
        </button>
      </div>

      {/* Direct Links */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        marginBottom: '20px', 
        border: '1px solid #ddd',
        borderRadius: '5px'
      }}>
        <h2>Direct Links (for comparison):</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <a 
            href="/profile"
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#17a2b8', 
              color: 'white', 
              textDecoration: 'none', 
              borderRadius: '3px',
              display: 'inline-block'
            }}
          >
            Profile (Direct)
          </a>
          
          <a 
            href="/dashboard"
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#17a2b8', 
              color: 'white', 
              textDecoration: 'none', 
              borderRadius: '3px',
              display: 'inline-block'
            }}
          >
            Dashboard (Direct)
          </a>
          
          <a 
            href="/"
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#17a2b8', 
              color: 'white', 
              textDecoration: 'none', 
              borderRadius: '3px',
              display: 'inline-block'
            }}
          >
            Home (Direct)
          </a>
        </div>
      </div>

      {/* Logs */}
      <div style={{ 
        backgroundColor: '#1a1a1a', 
        color: '#00ff00', 
        padding: '15px', 
        borderRadius: '5px',
        fontFamily: 'monospace',
        fontSize: '12px',
        maxHeight: '300px',
        overflowY: 'auto'
      }}>
        <h2 style={{ color: '#00ff00', marginTop: 0 }}>Debug Logs:</h2>
        {logs.length === 0 ? (
          <p>No logs yet...</p>
        ) : (
          logs.map((log, index) => (
            <div key={index} style={{ marginBottom: '2px' }}>
              {log}
            </div>
          ))
        )}
      </div>

      {/* Instructions */}
      <div style={{ 
        backgroundColor: '#fff3cd', 
        padding: '15px', 
        marginTop: '20px',
        border: '1px solid #ffeaa7',
        borderRadius: '5px'
      }}>
        <h2 style={{ color: '#856404', marginTop: 0 }}>Test Instructions:</h2>
        <ol style={{ color: '#856404' }}>
          <li>First, navigate to the dashboard: <a href="/dashboard" style={{ color: '#007bff' }}>/dashboard</a></li>
          <li>Then come back to this page: <a href="/minimal-test" style={{ color: '#007bff' }}>/minimal-test</a></li>
          <li>Try the "Go to Profile" button above</li>
          <li>Watch the debug logs to see what happens</li>
          <li>If navigation fails, try the direct links for comparison</li>
          <li>Check browser console (F12) for additional errors</li>
        </ol>
        
        <p style={{ color: '#856404', marginBottom: 0 }}>
          <strong>Expected:</strong> Clicking "Go to Profile" should immediately show the profile page.<br/>
          <strong>If broken:</strong> URL changes but page content doesn't update.
        </p>
      </div>
    </div>
  );
}
