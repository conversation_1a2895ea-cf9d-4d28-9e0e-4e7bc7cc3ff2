import { useRouter } from 'next/router';
import { useAuth } from '../lib/auth';
import { useState, useEffect } from 'react';

export default function HydrationTest() {
  const router = useRouter();
  const { user } = useAuth();
  const [isClient, setIsClient] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [routerState, setRouterState] = useState({
    pathname: '',
    asPath: '',
    query: {},
    isReady: false
  });

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setLogs(prev => [...prev, logEntry]);
  };

  // Check if we're on the client side
  useEffect(() => {
    setIsClient(true);
    addLog('Component hydrated on client side');
  }, []);

  // Track router state changes
  useEffect(() => {
    setRouterState({
      pathname: router.pathname,
      asPath: router.asPath,
      query: router.query,
      isReady: router.isReady
    });
    addLog(`Router state updated: ${router.pathname}`);
  }, [router.pathname, router.asPath, router.query, router.isReady]);

  // Track router events
  useEffect(() => {
    const handleRouteChangeStart = (url: string) => {
      addLog(`Route change started: ${url}`);
    };

    const handleRouteChangeComplete = (url: string) => {
      addLog(`Route change completed: ${url}`);
    };

    const handleRouteChangeError = (err: any, url: string) => {
      addLog(`Route change error: ${err.message} for ${url}`);
    };

    const handleBeforeHistoryChange = (url: string) => {
      addLog(`Before history change: ${url}`);
    };

    router.events.on('routeChangeStart', handleRouteChangeStart);
    router.events.on('routeChangeComplete', handleRouteChangeComplete);
    router.events.on('routeChangeError', handleRouteChangeError);
    router.events.on('beforeHistoryChange', handleBeforeHistoryChange);

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeStart);
      router.events.off('routeChangeComplete', handleRouteChangeComplete);
      router.events.off('routeChangeError', handleRouteChangeError);
      router.events.off('beforeHistoryChange', handleBeforeHistoryChange);
    };
  }, [router]);

  const testNavigation = async (url: string) => {
    addLog(`=== Testing navigation to: ${url} ===`);
    addLog(`Current pathname: ${router.pathname}`);
    addLog(`Router ready: ${router.isReady}`);
    addLog(`Is client: ${isClient}`);
    
    try {
      addLog('Calling router.push...');
      const startTime = Date.now();
      
      const result = await router.push(url);
      
      const endTime = Date.now();
      addLog(`Router.push completed in ${endTime - startTime}ms`);
      addLog(`Result: ${JSON.stringify(result)}`);
      
      // Check if the route actually changed
      setTimeout(() => {
        addLog(`Post-navigation check: ${router.pathname}`);
        addLog(`Window location: ${window.location.pathname}`);
      }, 100);
      
    } catch (error) {
      addLog(`Navigation error: ${error}`);
      addLog(`Error type: ${typeof error}`);
      addLog(`Error message: ${error.message || 'No message'}`);
    }
  };

  const checkEnvironment = () => {
    addLog('=== Environment Check ===');
    addLog(`Next.js version: ${process.env.NEXT_PUBLIC_VERSION || 'Unknown'}`);
    addLog(`React version: ${React.version || 'Unknown'}`);
    addLog(`Node environment: ${process.env.NODE_ENV}`);
    addLog(`Window defined: ${typeof window !== 'undefined'}`);
    addLog(`Document defined: ${typeof document !== 'undefined'}`);
    addLog(`Navigator defined: ${typeof navigator !== 'undefined'}`);
    
    if (typeof window !== 'undefined') {
      addLog(`User agent: ${navigator.userAgent.substring(0, 50)}...`);
      addLog(`Window location: ${window.location.href}`);
      addLog(`History length: ${window.history.length}`);
    }
  };

  if (!isClient) {
    return (
      <div style={{ padding: '20px' }}>
        <h1>Loading... (Server-side rendering)</h1>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      padding: '20px', 
      fontFamily: 'monospace',
      backgroundColor: '#f0f0f0'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>🔍 Hydration & Router Test</h1>
      
      {/* Environment Info */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        marginBottom: '20px', 
        border: '1px solid #ddd',
        borderRadius: '5px'
      }}>
        <h2>Environment Info:</h2>
        <p><strong>Is Client:</strong> {isClient ? 'Yes' : 'No'}</p>
        <p><strong>User:</strong> {user?.email || 'Not logged in'}</p>
        <p><strong>Current Time:</strong> {new Date().toLocaleString()}</p>
        <button 
          onClick={checkEnvironment}
          style={{ 
            padding: '8px 12px', 
            backgroundColor: '#17a2b8', 
            color: 'white', 
            border: 'none', 
            borderRadius: '3px',
            cursor: 'pointer'
          }}
        >
          Check Environment
        </button>
      </div>

      {/* Router State */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        marginBottom: '20px', 
        border: '1px solid #ddd',
        borderRadius: '5px'
      }}>
        <h2>Router State:</h2>
        <p><strong>Pathname:</strong> {routerState.pathname}</p>
        <p><strong>As Path:</strong> {routerState.asPath}</p>
        <p><strong>Query:</strong> {JSON.stringify(routerState.query)}</p>
        <p><strong>Is Ready:</strong> {routerState.isReady ? 'Yes' : 'No'}</p>
        <p><strong>Window Location:</strong> {typeof window !== 'undefined' ? window.location.pathname : 'N/A'}</p>
      </div>

      {/* Navigation Tests */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        marginBottom: '20px', 
        border: '1px solid #ddd',
        borderRadius: '5px'
      }}>
        <h2>Navigation Tests:</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '10px' }}>
          <button 
            onClick={() => testNavigation('/profile')}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#007bff', 
              color: 'white', 
              border: 'none', 
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Test Profile
          </button>
          
          <button 
            onClick={() => testNavigation('/dashboard')}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#28a745', 
              color: 'white', 
              border: 'none', 
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Test Dashboard
          </button>
          
          <button 
            onClick={() => testNavigation('/')}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#6f42c1', 
              color: 'white', 
              border: 'none', 
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Test Home
          </button>
          
          <button 
            onClick={() => testNavigation('/login')}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#fd7e14', 
              color: 'white', 
              border: 'none', 
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Test Login
          </button>
        </div>
        
        <button 
          onClick={() => setLogs([])}
          style={{ 
            padding: '8px 12px', 
            backgroundColor: '#dc3545', 
            color: 'white', 
            border: 'none', 
            borderRadius: '3px',
            cursor: 'pointer'
          }}
        >
          Clear Logs
        </button>
      </div>

      {/* Debug Logs */}
      <div style={{ 
        backgroundColor: '#1a1a1a', 
        color: '#00ff00', 
        padding: '15px', 
        borderRadius: '5px',
        fontFamily: 'monospace',
        fontSize: '12px',
        maxHeight: '400px',
        overflowY: 'auto'
      }}>
        <h2 style={{ color: '#00ff00', marginTop: 0 }}>Debug Logs:</h2>
        {logs.length === 0 ? (
          <p>No logs yet...</p>
        ) : (
          logs.map((log, index) => (
            <div key={index} style={{ marginBottom: '2px' }}>
              {log}
            </div>
          ))
        )}
      </div>

      {/* Diagnostic Info */}
      <div style={{ 
        backgroundColor: '#fff3cd', 
        padding: '15px', 
        marginTop: '20px',
        border: '1px solid #ffeaa7',
        borderRadius: '5px'
      }}>
        <h2 style={{ color: '#856404', marginTop: 0 }}>Diagnostic Information:</h2>
        <ul style={{ color: '#856404' }}>
          <li><strong>Purpose:</strong> Test if hydration or React 19/Next.js 15 compatibility issues are causing routing problems</li>
          <li><strong>What to watch:</strong> Router events, navigation timing, error messages</li>
          <li><strong>Expected:</strong> Route change events should fire and navigation should complete</li>
          <li><strong>If broken:</strong> Router events may not fire or navigation may hang</li>
        </ul>
        
        <p style={{ color: '#856404', marginBottom: 0 }}>
          <strong>Next Steps:</strong> If this test shows issues, we may need to downgrade Next.js/React versions or use alternative navigation methods.
        </p>
      </div>
    </div>
  );
}
