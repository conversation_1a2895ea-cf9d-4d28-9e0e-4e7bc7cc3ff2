import { useState } from 'react';
import { useAuth } from '../lib/auth';
import Layout from '../components/Layout';
import Link from 'next/link';

export default function NavigationTest() {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setTestResults(prev => [...prev, `${icon} [${timestamp}] ${message}`]);
  };

  const testNavigation = (url: string, name: string) => {
    try {
      window.location.href = url;
      addResult(`Navigation to ${name} (${url}) initiated`, 'success');
    } catch (error) {
      addResult(`Navigation to ${name} failed: ${error}`, 'error');
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const navigationLinks = [
    { name: 'Home', url: '/', description: 'Landing page' },
    { name: 'Dashboard', url: '/dashboard', description: 'Main dashboard' },
    { name: 'Profile', url: '/profile', description: 'User profile' },
    { name: 'Login', url: '/login', description: 'Authentication page' },
    { name: 'Dev Auth', url: '/dev-auth', description: 'Development login' },
    { name: 'API Debug', url: '/api-debug', description: 'API testing' },
    { name: 'Connection Test', url: '/connection-test', description: 'Backend connectivity' },
  ];

  const roleSpecificLinks = [
    ...(user?.role === 'patient' ? [
      { name: 'My Requests', url: '/requests', description: 'Patient requests' },
      { name: 'Create Request', url: '/requests/create', description: 'New request' },
      { name: 'Find Nurses', url: '/nurses', description: 'Browse nurses' },
    ] : []),
    ...(user?.role === 'nurse' ? [
      { name: 'My Requests', url: '/requests', description: 'Nurse requests' },
    ] : []),
  ];

  return (
    <Layout title="Navigation Test">
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Navigation Test Dashboard</h1>
          
          {/* Current User Info */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">Current User Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
              <div>
                <p><strong>User:</strong> {user ? user.email : 'Not logged in'}</p>
                <p><strong>Role:</strong> {user ? user.role : 'N/A'}</p>
              </div>
              <div>
                <p><strong>Status:</strong> {user ? user.status : 'N/A'}</p>
                <p><strong>Name:</strong> {user ? user.name : 'N/A'}</p>
              </div>
              <div>
                <p><strong>Navigation:</strong> {user ? 'Authenticated' : 'Public only'}</p>
                <p><strong>Dropdown:</strong> {user ? 'Available' : 'Login required'}</p>
              </div>
            </div>
          </div>

          {/* Test Controls */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h2 className="text-lg font-semibold text-gray-900">Navigation Tests</h2>
              <button
                onClick={clearResults}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
              >
                Clear Results
              </button>
            </div>
            
            {/* Main Navigation Links */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-800 mb-3">Main Navigation</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {navigationLinks.map((link) => (
                  <button
                    key={link.url}
                    onClick={() => testNavigation(link.url, link.name)}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors text-sm"
                    title={link.description}
                  >
                    {link.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Role-Specific Links */}
            {roleSpecificLinks.length > 0 && (
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-800 mb-3">Role-Specific Navigation ({user?.role})</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {roleSpecificLinks.map((link) => (
                    <button
                      key={link.url}
                      onClick={() => testNavigation(link.url, link.name)}
                      className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors text-sm"
                      title={link.description}
                    >
                      {link.name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Direct Links */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-800 mb-3">Direct Links (No JavaScript)</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {navigationLinks.map((link) => (
                  <Link
                    key={`direct-${link.url}`}
                    href={link.url}
                    className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors text-sm text-center"
                    title={`Direct link to ${link.description}`}
                  >
                    {link.name}
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Test Results */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Test Results</h2>
            <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
              {testResults.length === 0 ? (
                <p className="text-gray-500">No test results yet. Click navigation buttons above to test.</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Navigation Instructions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">✅ How to Test Navigation</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>1. Click the user dropdown in the navbar (top right)</li>
                <li>2. Try clicking Profile, Dashboard, or other links</li>
                <li>3. Use the test buttons above to verify navigation</li>
                <li>4. Check that dropdown closes after clicking</li>
                <li>5. Verify all role-specific links work</li>
              </ul>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">🔧 Fixed Issues</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>✅ Dropdown now uses click instead of hover</li>
                <li>✅ Added proper state management</li>
                <li>✅ Click outside to close functionality</li>
                <li>✅ Links close dropdown on click</li>
                <li>✅ Enhanced visual feedback</li>
              </ul>
            </div>
          </div>

          {/* Current Page Info */}
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold text-yellow-900 mb-2">📍 Current Page Information</h3>
            <div className="text-sm text-yellow-700 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Server-side'}</p>
                <p><strong>Pathname:</strong> {typeof window !== 'undefined' ? window.location.pathname : 'N/A'}</p>
              </div>
              <div>
                <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : 'N/A'}</p>
                <p><strong>Viewport:</strong> {typeof window !== 'undefined' ? `${window.innerWidth}x${window.innerHeight}` : 'N/A'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
