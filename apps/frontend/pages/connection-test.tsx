import { useState } from 'react';
import Layout from '../components/Layout';

export default function ConnectionTest() {
  const [results, setResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testConnection = async () => {
    setLoading(true);
    setResults([]);
    
    try {
      addResult('🔄 Starting connection tests...');
      
      // Test 1: Basic backend connection
      try {
        const response = await fetch('http://localhost:3001/api/admin/stats');
        if (response.ok) {
          const data = await response.json();
          addResult(`✅ Backend connection: SUCCESS (${data.data?.totalUsers || 0} users)`);
        } else {
          addResult(`❌ Backend connection: FAILED (${response.status})`);
        }
      } catch (error) {
        addResult(`❌ Backend connection: ERROR - ${error}`);
      }

      // Test 2: Users endpoint
      try {
        const response = await fetch('http://localhost:3001/api/admin/users');
        if (response.ok) {
          const data = await response.json();
          const userCount = data.data?.data?.length || 0;
          addResult(`✅ Users endpoint: SUCCESS (${userCount} users found)`);
        } else {
          addResult(`❌ Users endpoint: FAILED (${response.status})`);
        }
      } catch (error) {
        addResult(`❌ Users endpoint: ERROR - ${error}`);
      }

      // Test 3: Frontend API service
      try {
        const { apiService } = await import('../lib/api');
        const stats = await apiService.getAdminStats();
        addResult(`✅ API Service stats: SUCCESS (${stats?.totalUsers || 0} users)`);
      } catch (error) {
        addResult(`❌ API Service stats: ERROR - ${error}`);
      }

      // Test 4: Frontend API service users
      try {
        const { apiService } = await import('../lib/api');
        const users = await apiService.getAllUsers();
        addResult(`✅ API Service users: SUCCESS (${users?.length || 0} users)`);
      } catch (error) {
        addResult(`❌ API Service users: ERROR - ${error}`);
      }

      addResult('🎉 Connection tests completed!');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <Layout title="Connection Test">
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Backend Connection Test</h1>
          
          <div className="mb-6">
            <div className="flex space-x-4">
              <button
                onClick={testConnection}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {loading ? 'Testing...' : 'Run Connection Test'}
              </button>
              
              <button
                onClick={clearResults}
                className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700 transition-colors"
              >
                Clear Results
              </button>
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-gray-900 text-green-400 rounded-lg p-4 min-h-96 max-h-96 overflow-y-auto font-mono text-sm">
            {results.length === 0 ? (
              <p className="text-gray-500">No test results yet. Click "Run Connection Test" to start.</p>
            ) : (
              results.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
          </div>

          {/* Quick Info */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Expected Results</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>✅ Backend connection: SUCCESS (12 users)</li>
                <li>✅ Users endpoint: SUCCESS (12 users found)</li>
                <li>✅ API Service stats: SUCCESS (12 users)</li>
                <li>✅ API Service users: SUCCESS (12 users)</li>
              </ul>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">Backend Status</h3>
              <div className="text-sm text-green-700 space-y-1">
                <p><strong>Backend URL:</strong> http://localhost:3001</p>
                <p><strong>Stats Endpoint:</strong> /api/admin/stats</p>
                <p><strong>Users Endpoint:</strong> /api/admin/users</p>
                <p><strong>Expected Users:</strong> 12</p>
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="mt-6 border-t pt-6">
            <h3 className="font-semibold text-gray-900 mb-3">Quick Navigation</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <a href="/dashboard" className="bg-blue-600 text-white text-center px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                Dashboard
              </a>
              <a href="/api-debug" className="bg-green-600 text-white text-center px-4 py-2 rounded hover:bg-green-700 transition-colors">
                API Debug
              </a>
              <a href="/dev-auth" className="bg-yellow-600 text-white text-center px-4 py-2 rounded hover:bg-yellow-700 transition-colors">
                Dev Auth
              </a>
              <a href="http://localhost:3001/api/admin/stats" target="_blank" className="bg-purple-600 text-white text-center px-4 py-2 rounded hover:bg-purple-700 transition-colors">
                Direct API
              </a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
