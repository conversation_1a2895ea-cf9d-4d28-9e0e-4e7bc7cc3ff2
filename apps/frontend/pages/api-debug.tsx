import { useState, useEffect } from 'react';
import { useAuth } from '../lib/auth';
import { apiService } from '../lib/api';
import Layout from '../components/Layout';

export default function ApiDebug() {
  const { user } = useAuth();
  const [adminStats, setAdminStats] = useState<any>(null);
  const [allUsers, setAllUsers] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const testAdminStats = async () => {
    try {
      setLoading(true);
      setError('');
      console.log('Testing admin stats API...');
      
      const stats = await apiService.getAdminStats();
      console.log('Raw admin stats response:', stats);
      setAdminStats(stats);
    } catch (err: any) {
      console.error('Admin stats error:', err);
      setError(`Admin stats error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testAllUsers = async () => {
    try {
      setLoading(true);
      setError('');
      console.log('Testing all users API...');
      
      const users = await apiService.getAllUsers();
      console.log('Raw all users response:', users);
      setAllUsers(users);
    } catch (err: any) {
      console.error('All users error:', err);
      setError(`All users error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectAPI = async () => {
    try {
      setLoading(true);
      setError('');
      console.log('Testing direct API calls...');

      // Test admin stats
      const statsResponse = await fetch('http://localhost:3001/api/admin/stats');
      const statsData = await statsResponse.json();
      console.log('Direct admin stats:', statsData);

      // Test admin users
      const usersResponse = await fetch('http://localhost:3001/api/admin/users');
      const usersData = await usersResponse.json();
      console.log('Direct admin users:', usersData);

      setAdminStats(statsData);
      setAllUsers(usersData);

      // Show success message
      setError(`✅ Success! Stats: ${statsData.data?.totalUsers || 0} users, Users: ${usersData.data?.data?.length || 0} records`);
    } catch (err: any) {
      console.error('Direct API error:', err);
      setError(`❌ Direct API error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout title="API Debug">
      <div className="max-w-7xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">API Debug Dashboard</h1>
          
          {/* Current User Info */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">Current User</h2>
            <div className="text-sm text-blue-700">
              <p><strong>Email:</strong> {user?.email || 'Not logged in'}</p>
              <p><strong>Role:</strong> {user?.role || 'N/A'}</p>
              <p><strong>Status:</strong> {user?.status || 'N/A'}</p>
            </div>
          </div>

          {/* Test Buttons */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">API Tests</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <button
                onClick={testAdminStats}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                Test Admin Stats
              </button>
              
              <button
                onClick={testAllUsers}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50 transition-colors"
              >
                Test All Users
              </button>
              
              <button
                onClick={testDirectAPI}
                disabled={loading}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50 transition-colors"
              >
                Test Direct API
              </button>
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
              <p className="text-yellow-800">Loading...</p>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 rounded-lg">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {/* Admin Stats Results */}
          {adminStats && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Admin Stats Response</h2>
              <div className="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
                <pre className="text-xs text-gray-700">
                  {JSON.stringify(adminStats, null, 2)}
                </pre>
              </div>
              
              {/* Processed Stats */}
              {adminStats && (
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-100 p-3 rounded">
                    <p className="text-sm text-blue-600">Total Users</p>
                    <p className="text-xl font-bold text-blue-900">
                      {adminStats.data?.totalUsers || adminStats.totalUsers || 0}
                    </p>
                  </div>
                  <div className="bg-green-100 p-3 rounded">
                    <p className="text-sm text-green-600">Total Nurses</p>
                    <p className="text-xl font-bold text-green-900">
                      {adminStats.data?.totalNurses || adminStats.totalNurses || 0}
                    </p>
                  </div>
                  <div className="bg-purple-100 p-3 rounded">
                    <p className="text-sm text-purple-600">Total Requests</p>
                    <p className="text-xl font-bold text-purple-900">
                      {adminStats.data?.totalRequests || adminStats.totalRequests || 0}
                    </p>
                  </div>
                  <div className="bg-orange-100 p-3 rounded">
                    <p className="text-sm text-orange-600">Pending Nurses</p>
                    <p className="text-xl font-bold text-orange-900">
                      {adminStats.data?.pendingNurses || adminStats.pendingNurses || 0}
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* All Users Results */}
          {allUsers && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">All Users Response</h2>
              <div className="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
                <pre className="text-xs text-gray-700">
                  {JSON.stringify(allUsers, null, 2)}
                </pre>
              </div>
              
              {/* User Count */}
              <div className="mt-4">
                <p className="text-sm text-gray-600">
                  Users found: {
                    Array.isArray(allUsers) ? allUsers.length :
                    Array.isArray(allUsers?.data) ? allUsers.data.length :
                    allUsers?.data?.data ? allUsers.data.data.length : 0
                  }
                </p>
              </div>
            </div>
          )}

          {/* Quick Links */}
          <div className="border-t pt-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Links</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <a href="/dashboard" className="bg-blue-600 text-white text-center px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                Dashboard
              </a>
              <a href="/final-test" className="bg-green-600 text-white text-center px-4 py-2 rounded hover:bg-green-700 transition-colors">
                Final Test
              </a>
              <a href="/dev-auth" className="bg-yellow-600 text-white text-center px-4 py-2 rounded hover:bg-yellow-700 transition-colors">
                Dev Auth
              </a>
              <a href="http://localhost:3001/api/admin/stats" target="_blank" className="bg-purple-600 text-white text-center px-4 py-2 rounded hover:bg-purple-700 transition-colors">
                Direct API
              </a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
