import { useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../lib/auth';
import { apiService } from '../lib/api';

// Step 1: License & Experience
function Step1({ formData, setFormData, onNext }: any) {
  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Step 1: License & Experience</h2>
      <div className="mb-4">
        <label className="block mb-1 font-medium">License Number *</label>
        <input type="text" className="w-full border rounded px-3 py-2" required
          value={formData.licenseNumber || ''}
          onChange={e => setFormData((f: any) => ({ ...f, licenseNumber: e.target.value }))}
        />
      </div>
      <div className="mb-4">
        <label className="block mb-1 font-medium">Years of Experience *</label>
        <input type="number" min={0} className="w-full border rounded px-3 py-2" required
          value={formData.yearsOfExperience || ''}
          onChange={e => setFormData((f: any) => ({ ...f, yearsOfExperience: e.target.value }))}
        />
      </div>
      <div className="mb-4">
        <label className="block mb-1 font-medium">Specializations *</label>
        <input type="text" className="w-full border rounded px-3 py-2" required
          placeholder="e.g. general, pediatric"
          value={formData.specializations || ''}
          onChange={e => setFormData((f: any) => ({ ...f, specializations: e.target.value }))}
        />
      </div>
      <button className="bg-blue-600 text-white px-6 py-2 rounded" onClick={onNext}>Next</button>
    </div>
  );
}

// Step 2: Education & Certifications
function Step2({ formData, setFormData, onNext, onBack }: any) {
  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Step 2: Education & Certifications</h2>
      <div className="mb-4">
        <label className="block mb-1 font-medium">Education *</label>
        <input type="text" className="w-full border rounded px-3 py-2" required
          value={formData.education || ''}
          onChange={e => setFormData((f: any) => ({ ...f, education: e.target.value }))}
        />
      </div>
      <div className="mb-4">
        <label className="block mb-1 font-medium">Certifications *</label>
        <input type="text" className="w-full border rounded px-3 py-2" required
          placeholder="e.g. CPR Certified, BLS"
          value={formData.certifications || ''}
          onChange={e => setFormData((f: any) => ({ ...f, certifications: e.target.value }))}
        />
      </div>
      <div className="flex gap-2">
        <button className="bg-gray-300 px-6 py-2 rounded" onClick={onBack}>Back</button>
        <button className="bg-blue-600 text-white px-6 py-2 rounded" onClick={onNext}>Next</button>
      </div>
    </div>
  );
}

// Step 3: Bio, Hourly Rate, Languages
function Step3({ formData, setFormData, onBack, onSubmit, loading }: any) {
  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Step 3: Bio, Hourly Rate & Languages</h2>
      <div className="mb-4">
        <label className="block mb-1 font-medium">Bio *</label>
        <textarea className="w-full border rounded px-3 py-2" required
          value={formData.bio || ''}
          onChange={e => setFormData((f: any) => ({ ...f, bio: e.target.value }))}
        />
      </div>
      <div className="mb-4">
        <label className="block mb-1 font-medium">Hourly Rate (EGP) *</label>
        <input type="number" min={0} className="w-full border rounded px-3 py-2" required
          value={formData.hourlyRate || ''}
          onChange={e => setFormData((f: any) => ({ ...f, hourlyRate: e.target.value }))}
        />
      </div>
      <div className="mb-4">
        <label className="block mb-1 font-medium">Languages *</label>
        <input type="text" className="w-full border rounded px-3 py-2" required
          placeholder="e.g. Arabic, English"
          value={formData.languages || ''}
          onChange={e => setFormData((f: any) => ({ ...f, languages: e.target.value }))}
        />
      </div>
      <div className="flex gap-2">
        <button className="bg-gray-300 px-6 py-2 rounded" onClick={onBack}>Back</button>
        <button className="bg-green-600 text-white px-6 py-2 rounded" onClick={onSubmit} disabled={loading}>{loading ? 'Submitting...' : 'Submit'}</button>
      </div>
    </div>
  );
}

export default function NurseProfileCompletion() {
  const { user, updateUser } = useAuth();
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // If not nurse or already verified, redirect
  if (!user || user.role !== 'nurse' || user.status !== 'pending') {
    if (typeof window !== 'undefined') router.replace('/');
    return null;
  }

  // Save each step to backend
  const saveStep = async (data: any) => {
    setLoading(true);
    setError('');
    try {
      // Save partial profile (simulate API call)
      await apiService.updateProfile({ nurseProfile: data });
      updateUser({ nurseProfile: { ...user.nurseProfile, ...data } });
    } catch (e: any) {
      setError(e.message || 'Failed to save');
    } finally {
      setLoading(false);
    }
  };

  const handleNext = async () => {
    if (step === 1) {
      if (!formData.licenseNumber || !formData.yearsOfExperience || !formData.specializations) {
        setError('All fields are required');
        return;
      }
      await saveStep({
        licenseNumber: formData.licenseNumber,
        yearsOfExperience: Number(formData.yearsOfExperience),
        specializations: formData.specializations.split(',').map((s: string) => s.trim()),
      });
      setStep(2);
    } else if (step === 2) {
      if (!formData.education || !formData.certifications) {
        setError('All fields are required');
        return;
      }
      await saveStep({
        education: formData.education,
        certifications: formData.certifications.split(',').map((c: string) => c.trim()),
      });
      setStep(3);
    }
  };

  const handleBack = () => setStep(step - 1);

  const handleSubmit = async () => {
    if (!formData.bio || !formData.hourlyRate || !formData.languages) {
      setError('All fields are required');
      return;
    }
    setLoading(true);
    setError('');
    try {
      await apiService.updateProfile({
        nurseProfile: {
          ...formData,
          hourlyRate: Number(formData.hourlyRate),
          languages: formData.languages.split(',').map((l: string) => l.trim()),
        },
        status: 'pending',
      });
      // After submission, reload user and redirect
      const updated = await apiService.getProfile();
      updateUser(updated);
      router.replace('/verification-pending');
    } catch (e: any) {
      setError(e.message || 'Failed to submit');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-lg w-full bg-white shadow-lg rounded-lg p-8">
        <div className="mb-6 flex items-center justify-between">
          <h1 className="text-2xl font-bold">Complete Your Profile</h1>
          <span className="text-sm text-gray-500">Step {step} of 3</span>
        </div>
        {error && <div className="bg-red-100 text-red-700 p-2 rounded mb-4">{error}</div>}
        {step === 1 && <Step1 formData={formData} setFormData={setFormData} onNext={handleNext} />}
        {step === 2 && <Step2 formData={formData} setFormData={setFormData} onNext={handleNext} onBack={handleBack} />}
        {step === 3 && <Step3 formData={formData} setFormData={setFormData} onBack={handleBack} onSubmit={handleSubmit} loading={loading} />}
      </div>
    </div>
  );
}
