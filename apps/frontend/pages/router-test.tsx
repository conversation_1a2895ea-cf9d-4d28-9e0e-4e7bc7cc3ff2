import { useRouter } from 'next/router';
import { useAuth } from '../lib/auth';
import Link from 'next/link';
import { useState } from 'react';

export default function RouterTest() {
  const router = useRouter();
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setTestResults(prev => [...prev, `${icon} [${timestamp}] ${message}`]);
  };

  const testNavigation = async (url: string, method: string) => {
    try {
      addResult(`Testing ${method} navigation to ${url}`, 'info');
      
      if (method === 'router.push') {
        await router.push(url);
        addResult(`${method} to ${url}: SUCCESS`, 'success');
      } else if (method === 'window.location') {
        window.location.href = url;
        addResult(`${method} to ${url}: INITIATED`, 'success');
      } else if (method === 'window.open') {
        window.open(url, '_self');
        addResult(`${method} to ${url}: INITIATED`, 'success');
      }
    } catch (error) {
      addResult(`${method} to ${url}: ERROR - ${error}`, 'error');
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Router Navigation Test</h1>
        
        {/* Current Status */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">Current Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
            <div>
              <p><strong>Current Route:</strong> {router.pathname}</p>
              <p><strong>Query:</strong> {JSON.stringify(router.query)}</p>
            </div>
            <div>
              <p><strong>User:</strong> {user ? user.email : 'Not logged in'}</p>
              <p><strong>Role:</strong> {user ? user.role : 'N/A'}</p>
            </div>
          </div>
        </div>

        {/* Navigation Tests */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold text-gray-900">Navigation Methods Test</h2>
            <button
              onClick={clearResults}
              className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
            >
              Clear Results
            </button>
          </div>

          {/* Router.push Tests */}
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 mb-2">Next.js Router.push</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <button
                onClick={() => testNavigation('/profile', 'router.push')}
                className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors text-sm"
              >
                Profile
              </button>
              <button
                onClick={() => testNavigation('/dashboard', 'router.push')}
                className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 transition-colors text-sm"
              >
                Dashboard
              </button>
              <button
                onClick={() => testNavigation('/', 'router.push')}
                className="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 transition-colors text-sm"
              >
                Home
              </button>
              <button
                onClick={() => testNavigation('/login', 'router.push')}
                className="bg-orange-600 text-white px-3 py-2 rounded hover:bg-orange-700 transition-colors text-sm"
              >
                Login
              </button>
            </div>
          </div>

          {/* Window.location Tests */}
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 mb-2">Window.location</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <button
                onClick={() => testNavigation('/profile', 'window.location')}
                className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors text-sm"
              >
                Profile
              </button>
              <button
                onClick={() => testNavigation('/dashboard', 'window.location')}
                className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 transition-colors text-sm"
              >
                Dashboard
              </button>
              <button
                onClick={() => testNavigation('/', 'window.location')}
                className="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 transition-colors text-sm"
              >
                Home
              </button>
              <button
                onClick={() => testNavigation('/login', 'window.location')}
                className="bg-orange-600 text-white px-3 py-2 rounded hover:bg-orange-700 transition-colors text-sm"
              >
                Login
              </button>
            </div>
          </div>

          {/* Link Component Tests */}
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 mb-2">Next.js Link Component</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Link
                href="/profile"
                className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors text-sm text-center"
              >
                Profile
              </Link>
              <Link
                href="/dashboard"
                className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 transition-colors text-sm text-center"
              >
                Dashboard
              </Link>
              <Link
                href="/"
                className="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 transition-colors text-sm text-center"
              >
                Home
              </Link>
              <Link
                href="/login"
                className="bg-orange-600 text-white px-3 py-2 rounded hover:bg-orange-700 transition-colors text-sm text-center"
              >
                Login
              </Link>
            </div>
          </div>

          {/* Regular HTML Links */}
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 mb-2">Regular HTML Links</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <a
                href="/profile"
                className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors text-sm text-center block"
              >
                Profile
              </a>
              <a
                href="/dashboard"
                className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 transition-colors text-sm text-center block"
              >
                Dashboard
              </a>
              <a
                href="/"
                className="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 transition-colors text-sm text-center block"
              >
                Home
              </a>
              <a
                href="/login"
                className="bg-orange-600 text-white px-3 py-2 rounded hover:bg-orange-700 transition-colors text-sm text-center block"
              >
                Login
              </a>
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Test Results</h2>
          <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet. Try the navigation buttons above.</p>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Debug Info */}
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h3 className="font-semibold text-yellow-900 mb-2">🔍 Debug Information</h3>
          <div className="text-sm text-yellow-700 space-y-1">
            <p><strong>Router Ready:</strong> {router.isReady ? 'Yes' : 'No'}</p>
            <p><strong>Router Events:</strong> Available</p>
            <p><strong>Window Location:</strong> {typeof window !== 'undefined' ? window.location.href : 'Server-side'}</p>
            <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : 'N/A'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
