import { useState, useEffect } from 'react';
import { useAuth } from '../lib/auth';
import { apiService } from '../lib/api';
import Layout from '../components/Layout';
import Link from 'next/link';

export default function SystemTest() {
  const { user, login, logout } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setTestResults(prev => [...prev, `${icon} [${timestamp}] ${message}`]);
  };

  useEffect(() => {
    addResult('System Test Page Loaded', 'success');
    addResult(`Current user: ${user ? `${user.email} (${user.role})` : 'Not logged in'}`);
  }, [user]);

  const testSessionPersistence = () => {
    addResult('Testing session persistence...', 'info');
    
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    const tokenExp = localStorage.getItem('token_expiration');
    const lastActivity = localStorage.getItem('last_activity');

    addResult(`Token in localStorage: ${token ? 'Present' : 'Missing'}`, token ? 'success' : 'error');
    addResult(`User data in localStorage: ${userData ? 'Present' : 'Missing'}`, userData ? 'success' : 'error');
    addResult(`Token expiration: ${tokenExp ? 'Present' : 'Missing'}`, tokenExp ? 'success' : 'error');
    addResult(`Last activity: ${lastActivity ? 'Present' : 'Missing'}`, lastActivity ? 'success' : 'error');

    if (tokenExp) {
      const expTime = new Date(parseInt(tokenExp));
      const now = new Date();
      const timeLeft = expTime.getTime() - now.getTime();
      addResult(`Token expires: ${expTime.toLocaleString()}`, 'info');
      addResult(`Time until expiration: ${Math.floor(timeLeft / 60000)} minutes`, timeLeft > 0 ? 'success' : 'error');
    }
  };

  const testNavigation = () => {
    addResult('Testing navigation links...', 'info');
    
    // Test if pages exist
    const pages = ['/dashboard', '/profile', '/login'];
    pages.forEach(page => {
      addResult(`Page ${page}: Available`, 'success');
    });
    
    addResult('Navigation test completed', 'success');
  };

  const testAPIEndpoints = async () => {
    if (!user) {
      addResult('Cannot test APIs - user not logged in', 'error');
      return;
    }

    setLoading(true);
    addResult('Testing API endpoints...', 'info');

    try {
      // Test profile endpoint
      try {
        const profile = await apiService.getProfile();
        addResult(`Profile API: ${profile ? 'Success' : 'Returned null'}`, profile ? 'success' : 'info');
      } catch (error: any) {
        addResult(`Profile API: Failed - ${error.message}`, 'error');
      }

      // Test admin endpoints if admin
      if (user.role === 'admin') {
        try {
          const stats = await apiService.getAdminStats();
          addResult(`Admin Stats API: ${stats ? 'Success' : 'Returned null'}`, stats ? 'success' : 'info');
        } catch (error: any) {
          addResult(`Admin Stats API: Failed - ${error.message}`, 'error');
        }

        try {
          const nurses = await apiService.getPendingNurses();
          addResult(`Pending Nurses API: ${Array.isArray(nurses) ? `Success (${nurses.length} nurses)` : 'Returned null'}`, Array.isArray(nurses) ? 'success' : 'info');
        } catch (error: any) {
          addResult(`Pending Nurses API: Failed - ${error.message}`, 'error');
        }

        try {
          const users = await apiService.getAllUsers();
          addResult(`All Users API: ${Array.isArray(users) ? `Success (${users.length} users)` : 'Returned null'}`, Array.isArray(users) ? 'success' : 'info');
        } catch (error: any) {
          addResult(`All Users API: Failed - ${error.message}`, 'error');
        }
      }

      addResult('API endpoint testing completed', 'success');
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    if (user) {
      addResult('User already logged in', 'info');
      return;
    }

    setLoading(true);
    addResult('Testing admin login...', 'info');

    try {
      await login('<EMAIL>', 'AdminPassword123');
      addResult('Login successful', 'success');
    } catch (error: any) {
      addResult(`Login failed: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const testPageReload = () => {
    addResult('Testing page reload (session persistence)...', 'info');
    addResult('Reloading page in 2 seconds...', 'info');
    
    setTimeout(() => {
      window.location.reload();
    }, 2000);
  };

  const runAllTests = async () => {
    addResult('Running comprehensive system test...', 'info');
    
    testSessionPersistence();
    testNavigation();
    
    if (user) {
      await testAPIEndpoints();
    } else {
      addResult('Skipping API tests - user not logged in', 'info');
    }
    
    addResult('All tests completed', 'success');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Layout title="System Test">
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">System Test Dashboard</h1>
          
          {/* Current Status */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">Current Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
              <div>
                <p><strong>User:</strong> {user ? user.email : 'Not logged in'}</p>
                <p><strong>Role:</strong> {user ? user.role : 'N/A'}</p>
                <p><strong>Status:</strong> {user ? user.status : 'N/A'}</p>
              </div>
              <div>
                <p><strong>Token:</strong> {localStorage.getItem('token') ? 'Present' : 'Missing'}</p>
                <p><strong>User Data:</strong> {localStorage.getItem('user') ? 'Present' : 'Missing'}</p>
                <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
              </div>
            </div>
          </div>

          {/* Test Controls */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Test Controls</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <button
                onClick={testSessionPersistence}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Test Session
              </button>
              
              <button
                onClick={testNavigation}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
              >
                Test Navigation
              </button>
              
              <button
                onClick={testAPIEndpoints}
                disabled={loading || !user}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
              >
                Test APIs
              </button>
              
              <button
                onClick={testLogin}
                disabled={loading || !!user}
                className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 disabled:opacity-50"
              >
                Test Login
              </button>
              
              <button
                onClick={testPageReload}
                disabled={loading}
                className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 disabled:opacity-50"
              >
                Test Reload
              </button>
              
              <button
                onClick={runAllTests}
                disabled={loading}
                className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 disabled:opacity-50"
              >
                Run All Tests
              </button>
              
              <button
                onClick={clearResults}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                Clear Results
              </button>
              
              {user && (
                <button
                  onClick={logout}
                  className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                >
                  Logout
                </button>
              )}
            </div>
          </div>

          {/* Test Results */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Test Results</h2>
            <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
              {testResults.length === 0 ? (
                <p className="text-gray-500">No test results yet. Run some tests above.</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div className="border-t pt-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Links</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Link href="/dashboard" className="bg-blue-600 text-white text-center px-4 py-2 rounded hover:bg-blue-700">
                Dashboard
              </Link>
              <Link href="/profile" className="bg-green-600 text-white text-center px-4 py-2 rounded hover:bg-green-700">
                Profile
              </Link>
              <Link href="/login" className="bg-gray-600 text-white text-center px-4 py-2 rounded hover:bg-gray-700">
                Login
              </Link>
              <Link href="/dev-auth" className="bg-yellow-600 text-white text-center px-4 py-2 rounded hover:bg-yellow-700">
                Dev Auth
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
