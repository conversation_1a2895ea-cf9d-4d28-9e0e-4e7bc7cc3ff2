import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../lib/auth';
import Link from 'next/link';

export default function RoutingDebug() {
  const router = useRouter();
  const { user } = useAuth();
  const [routeHistory, setRouteHistory] = useState<string[]>([]);
  const [renderCount, setRenderCount] = useState(0);

  // Track renders
  useEffect(() => {
    setRenderCount(prev => prev + 1);
    console.log('RoutingDebug component rendered:', renderCount + 1);
  });

  // Track route changes
  useEffect(() => {
    const handleRouteChange = (url: string) => {
      console.log('Route changed to:', url);
      setRouteHistory(prev => [...prev, `${new Date().toLocaleTimeString()}: ${url}`]);
    };

    const handleRouteChangeStart = (url: string) => {
      console.log('Route change started to:', url);
    };

    const handleRouteChangeComplete = (url: string) => {
      console.log('Route change completed to:', url);
    };

    const handleRouteChangeError = (err: any, url: string) => {
      console.error('Route change error:', err, 'URL:', url);
    };

    router.events.on('routeChangeStart', handleRouteChangeStart);
    router.events.on('routeChangeComplete', handleRouteChangeComplete);
    router.events.on('routeChangeError', handleRouteChangeError);
    router.events.on('routeChangeComplete', handleRouteChange);

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeStart);
      router.events.off('routeChangeComplete', handleRouteChangeComplete);
      router.events.off('routeChangeError', handleRouteChangeError);
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router]);

  const testNavigation = async (url: string, method: string) => {
    console.log(`Testing ${method} navigation to ${url}`);
    
    try {
      if (method === 'router.push') {
        await router.push(url);
      } else if (method === 'router.replace') {
        await router.replace(url);
      } else if (method === 'window.location') {
        window.location.href = url;
      }
    } catch (error) {
      console.error(`Navigation error with ${method}:`, error);
    }
  };

  const clearHistory = () => {
    setRouteHistory([]);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">🔍 Routing Debug Dashboard</h1>
        
        {/* Current Status */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">Current Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
            <div>
              <p><strong>Current Route:</strong> {router.pathname}</p>
              <p><strong>Query:</strong> {JSON.stringify(router.query)}</p>
              <p><strong>As Path:</strong> {router.asPath}</p>
            </div>
            <div>
              <p><strong>Router Ready:</strong> {router.isReady ? 'Yes' : 'No'}</p>
              <p><strong>Render Count:</strong> {renderCount}</p>
              <p><strong>User:</strong> {user?.email || 'Not logged in'}</p>
            </div>
            <div>
              <p><strong>Browser URL:</strong> {typeof window !== 'undefined' ? window.location.pathname : 'N/A'}</p>
              <p><strong>History Length:</strong> {typeof window !== 'undefined' ? window.history.length : 'N/A'}</p>
              <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Other' : 'N/A'}</p>
            </div>
          </div>
        </div>

        {/* Navigation Tests */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Navigation Methods Test</h2>
          
          {/* Router.push Tests */}
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 mb-2">Router.push (Recommended)</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              <button
                onClick={() => testNavigation('/', 'router.push')}
                className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors text-sm"
              >
                Home
              </button>
              <button
                onClick={() => testNavigation('/profile', 'router.push')}
                className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 transition-colors text-sm"
              >
                Profile
              </button>
              <button
                onClick={() => testNavigation('/dashboard', 'router.push')}
                className="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 transition-colors text-sm"
              >
                Dashboard
              </button>
              <button
                onClick={() => testNavigation('/login', 'router.push')}
                className="bg-orange-600 text-white px-3 py-2 rounded hover:bg-orange-700 transition-colors text-sm"
              >
                Login
              </button>
              <button
                onClick={() => testNavigation('/test-dashboard', 'router.push')}
                className="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 transition-colors text-sm"
              >
                Test Dashboard
              </button>
            </div>
          </div>

          {/* Link Components */}
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 mb-2">Next.js Link Components</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              <Link href="/" className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors text-sm text-center">
                Home
              </Link>
              <Link href="/profile" className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 transition-colors text-sm text-center">
                Profile
              </Link>
              <Link href="/dashboard" className="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 transition-colors text-sm text-center">
                Dashboard
              </Link>
              <Link href="/login" className="bg-orange-600 text-white px-3 py-2 rounded hover:bg-orange-700 transition-colors text-sm text-center">
                Login
              </Link>
              <Link href="/test-dashboard" className="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 transition-colors text-sm text-center">
                Test Dashboard
              </Link>
            </div>
          </div>

          {/* Window.location (Full Page Reload) */}
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 mb-2">Window.location (Full Reload)</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              <button
                onClick={() => testNavigation('/', 'window.location')}
                className="bg-gray-600 text-white px-3 py-2 rounded hover:bg-gray-700 transition-colors text-sm"
              >
                Home
              </button>
              <button
                onClick={() => testNavigation('/profile', 'window.location')}
                className="bg-gray-600 text-white px-3 py-2 rounded hover:bg-gray-700 transition-colors text-sm"
              >
                Profile
              </button>
              <button
                onClick={() => testNavigation('/dashboard', 'window.location')}
                className="bg-gray-600 text-white px-3 py-2 rounded hover:bg-gray-700 transition-colors text-sm"
              >
                Dashboard
              </button>
              <button
                onClick={() => testNavigation('/login', 'window.location')}
                className="bg-gray-600 text-white px-3 py-2 rounded hover:bg-gray-700 transition-colors text-sm"
              >
                Login
              </button>
              <button
                onClick={() => testNavigation('/test-dashboard', 'window.location')}
                className="bg-gray-600 text-white px-3 py-2 rounded hover:bg-gray-700 transition-colors text-sm"
              >
                Test Dashboard
              </button>
            </div>
          </div>
        </div>

        {/* Route History */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold text-gray-900">Route Change History</h2>
            <button
              onClick={clearHistory}
              className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
            >
              Clear History
            </button>
          </div>
          <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
            {routeHistory.length === 0 ? (
              <p className="text-gray-500">No route changes recorded yet. Try navigating using the buttons above.</p>
            ) : (
              routeHistory.map((entry, index) => (
                <div key={index} className="mb-1">
                  {entry}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Debug Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-900 mb-2">🧪 Debug Instructions</h3>
          <ol className="text-sm text-yellow-800 space-y-2">
            <li><strong>1. Test from Dashboard:</strong> Navigate to /dashboard first, then use this page to test navigation</li>
            <li><strong>2. Check Console:</strong> Open browser dev tools to see detailed routing logs</li>
            <li><strong>3. Compare Methods:</strong> Try router.push vs Link components vs window.location</li>
            <li><strong>4. Watch URL Bar:</strong> Note if URL changes but content doesn't update</li>
            <li><strong>5. Check Route History:</strong> See if route changes are being detected</li>
            <li><strong>6. Test Refresh:</strong> If content doesn't update, try manual refresh (F5)</li>
          </ol>
          
          <div className="mt-4 p-3 bg-yellow-100 rounded">
            <p className="text-sm text-yellow-800">
              <strong>Expected Behavior:</strong> All navigation methods should immediately show new page content. 
              If URL changes but content doesn't update, there's a client-side routing issue.
            </p>
          </div>
        </div>

        {/* Current Page Indicator */}
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-900 mb-2">✅ Current Page Confirmation</h3>
          <p className="text-sm text-green-700">
            You are currently viewing the <strong>Routing Debug</strong> page. 
            If you navigated here from the dashboard and can see this content immediately, 
            then navigation is working correctly.
          </p>
        </div>
      </div>
    </div>
  );
}
