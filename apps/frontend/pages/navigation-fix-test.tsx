import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../lib/auth';
import Layout from '../components/Layout';

export default function NavigationFixTest() {
  const router = useRouter();
  const { user } = useAuth();
  const [pageLoadTime] = useState(new Date().toLocaleTimeString());
  const [navigationLog, setNavigationLog] = useState<string[]>([]);

  useEffect(() => {
    const log = `Page loaded at ${pageLoadTime} - Route: ${router.pathname}`;
    console.log(log);
    setNavigationLog(prev => [...prev, log]);
  }, [router.pathname, pageLoadTime]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setNavigationLog(prev => [...prev, logEntry]);
  };

  const testRouterNavigation = async (url: string) => {
    addLog(`Testing router.push to ${url}`);
    try {
      await router.push(url);
      addLog(`Successfully navigated to ${url}`);
    } catch (error) {
      addLog(`Navigation failed to ${url}: ${error}`);
    }
  };

  if (!user) {
    return (
      <Layout title="Please Login">
        <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-4">Please login to test navigation.</p>
          <button 
            onClick={() => testRouterNavigation('/dev-auth')}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Navigation Fix Test">
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">🔧 Navigation Fix Test</h1>
          
          {/* Current Status */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">Current Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
              <div>
                <p><strong>Page Load Time:</strong> {pageLoadTime}</p>
                <p><strong>Current Route:</strong> {router.pathname}</p>
                <p><strong>User:</strong> {user.email}</p>
              </div>
              <div>
                <p><strong>Router Ready:</strong> {router.isReady ? 'Yes' : 'No'}</p>
                <p><strong>User Role:</strong> {user.role}</p>
                <p><strong>Navigation Method:</strong> Router-based buttons</p>
              </div>
            </div>
          </div>

          {/* Test Instructions */}
          <div className="mb-6 p-4 bg-green-50 rounded-lg">
            <h2 className="text-lg font-semibold text-green-900 mb-2">✅ Test Instructions</h2>
            <ol className="text-sm text-green-700 space-y-1">
              <li>1. <strong>Dropdown Test:</strong> Click your user dropdown in the header above</li>
              <li>2. <strong>Profile Navigation:</strong> Click "Profile" - should navigate immediately</li>
              <li>3. <strong>Dashboard Navigation:</strong> Click "Dashboard" - should navigate immediately</li>
              <li>4. <strong>Direct Test:</strong> Use the buttons below to test router.push</li>
              <li>5. <strong>Check Logs:</strong> Watch the navigation log below for debugging</li>
            </ol>
          </div>

          {/* Router Navigation Tests */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Router Navigation Tests</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <button
                onClick={() => testRouterNavigation('/profile')}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                📄 Profile
              </button>
              
              <button
                onClick={() => testRouterNavigation('/dashboard')}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
              >
                📊 Dashboard
              </button>
              
              <button
                onClick={() => testRouterNavigation('/')}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
              >
                🏠 Home
              </button>
              
              <button
                onClick={() => testRouterNavigation('/routing-debug')}
                className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors"
              >
                🔍 Debug
              </button>
            </div>
          </div>

          {/* Expected vs Actual Behavior */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">✅ Expected Behavior</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Click navigation link</li>
                <li>• URL changes immediately</li>
                <li>• Page content updates immediately</li>
                <li>• No manual refresh needed</li>
                <li>• Smooth client-side routing</li>
              </ul>
            </div>
            
            <div className="bg-red-50 p-4 rounded-lg">
              <h3 className="font-semibold text-red-900 mb-2">❌ Previous Problem</h3>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• Click navigation link</li>
                <li>• URL changes but content doesn't</li>
                <li>• Manual refresh (F5) required</li>
                <li>• Client-side routing broken</li>
                <li>• Poor user experience</li>
              </ul>
            </div>
          </div>

          {/* Navigation Log */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Navigation Log</h2>
            <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
              {navigationLog.length === 0 ? (
                <p className="text-gray-500">No navigation events yet...</p>
              ) : (
                navigationLog.map((entry, index) => (
                  <div key={index} className="mb-1">
                    {entry}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Fix Summary */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 mb-2">🔧 Applied Fixes</h3>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>✅ <strong>Layout Component:</strong> Replaced Link components with router.push buttons</li>
              <li>✅ <strong>Navigation Handler:</strong> Added proper async navigation with error handling</li>
              <li>✅ <strong>Dropdown Management:</strong> Improved state management for dropdowns</li>
              <li>✅ <strong>Debug Logging:</strong> Added comprehensive console logging</li>
              <li>✅ <strong>Fallback Navigation:</strong> Window.location fallback for failed router.push</li>
            </ul>
            
            <div className="mt-4 p-3 bg-yellow-100 rounded">
              <p className="text-sm text-yellow-800">
                <strong>Key Change:</strong> Navigation now uses <code>router.push()</code> instead of 
                Next.js Link components to ensure proper client-side routing from the dashboard.
              </p>
            </div>
          </div>

          {/* Success Indicator */}
          <div className="mt-6 p-4 bg-green-100 border border-green-300 rounded-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">
                  Navigation Fix Applied Successfully! If you can see this page content immediately after 
                  clicking navigation links, the client-side routing is now working correctly.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
