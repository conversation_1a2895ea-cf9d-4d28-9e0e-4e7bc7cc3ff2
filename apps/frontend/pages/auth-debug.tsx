import { useState, useEffect } from 'react';
import { useAuth } from '../lib/auth';
import { apiService } from '../lib/api';
import Layout from '../components/Layout';

export default function AuthDebug() {
  const { user, login, logout } = useAuth();
  const [logs, setLogs] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    addLog('AuthDebug component mounted');
    addLog(`User state: ${user ? `${user.email} (${user.role})` : 'Not logged in'}`);
  }, [user]);

  const testLogin = async () => {
    try {
      setLoading(true);
      addLog('🧪 Starting login test...');
      
      await login('<EMAIL>', 'AdminPassword123');
      addLog('✅ Login successful');
      
    } catch (error: any) {
      addLog(`❌ Login failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testAPIs = async () => {
    if (!user) {
      addLog('❌ No user logged in');
      return;
    }

    try {
      setLoading(true);
      addLog('🧪 Testing API calls...');

      // Test profile
      try {
        const profile = await apiService.getProfile();
        addLog(`✅ Profile: ${profile ? 'Success' : 'Null response'}`);
      } catch (error: any) {
        addLog(`❌ Profile failed: ${error.message}`);
      }

      // Test admin stats (if admin)
      if (user.role === 'admin') {
        try {
          const stats = await apiService.getAdminStats();
          addLog(`✅ Admin stats: ${stats ? 'Success' : 'Null response'}`);
        } catch (error: any) {
          addLog(`❌ Admin stats failed: ${error.message}`);
        }

        try {
          const nurses = await apiService.getPendingNurses();
          addLog(`✅ Pending nurses: ${Array.isArray(nurses) ? nurses.length : 'Null response'}`);
        } catch (error: any) {
          addLog(`❌ Pending nurses failed: ${error.message}`);
        }
      }

    } finally {
      setLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const testLogout = () => {
    addLog('🧪 Testing logout...');
    logout();
    addLog('✅ Logout completed');
  };

  return (
    <Layout title="Authentication Debug">
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Authentication Debug</h1>
          
          {/* Current Status */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">Current Status</h2>
            <div className="space-y-1 text-sm text-blue-800">
              <p><strong>Logged In:</strong> {user ? 'Yes' : 'No'}</p>
              {user && (
                <>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>Role:</strong> {user.role}</p>
                  <p><strong>Status:</strong> {user.status}</p>
                  <p><strong>Name:</strong> {user.name}</p>
                </>
              )}
              <p><strong>Token:</strong> {localStorage.getItem('token') ? 'Present' : 'Missing'}</p>
              <p><strong>User Data:</strong> {localStorage.getItem('user') ? 'Present' : 'Missing'}</p>
            </div>
          </div>

          {/* Test Controls */}
          <div className="mb-6 space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">Test Controls</h2>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={testLogin}
                disabled={loading || !!user}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Test Login
              </button>
              
              <button
                onClick={testAPIs}
                disabled={loading || !user}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
              >
                Test APIs
              </button>
              
              <button
                onClick={testLogout}
                disabled={loading || !user}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
              >
                Test Logout
              </button>
              
              <button
                onClick={clearLogs}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                Clear Logs
              </button>
            </div>
          </div>

          {/* Logs */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Debug Logs</h2>
            <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
              {logs.length === 0 ? (
                <p className="text-gray-500">No logs yet...</p>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div className="border-t pt-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Links</h2>
            <div className="flex flex-wrap gap-3">
              <a
                href="/dashboard"
                className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700"
              >
                Dashboard
              </a>
              <a
                href="/login"
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                Login Page
              </a>
              <a
                href="/dev-auth"
                className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700"
              >
                Dev Auth
              </a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
