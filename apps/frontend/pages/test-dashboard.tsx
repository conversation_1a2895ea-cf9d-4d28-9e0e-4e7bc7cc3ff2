import { useAuth } from '../lib/auth';
import SimpleLayout from '../components/SimpleLayout';
import Link from 'next/link';

export default function TestDashboard() {
  const { user } = useAuth();

  if (!user) {
    return (
      <SimpleLayout title="Please Login">
        <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-4">Please login to access the dashboard.</p>
          <Link 
            href="/dev-auth" 
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Login
          </Link>
        </div>
      </SimpleLayout>
    );
  }

  return (
    <SimpleLayout title={`Dashboard - ${user.name}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Welcome back, {user.name}! 👋
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-blue-50 p-3 rounded">
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Role:</strong> {user.role}</p>
            </div>
            <div className="bg-green-50 p-3 rounded">
              <p><strong>Status:</strong> {user.status}</p>
              <p><strong>Member since:</strong> {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</p>
            </div>
            <div className="bg-purple-50 p-3 rounded">
              <p><strong>Navigation:</strong> Working ✅</p>
              <p><strong>Layout:</strong> SimpleLayout</p>
            </div>
          </div>
        </div>

        {/* Navigation Test Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🧪 Navigation Test</h3>
          <p className="text-gray-600 mb-4">
            Test navigation by clicking the user dropdown in the header above, or use the buttons below:
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Link 
              href="/profile" 
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors text-center"
            >
              📄 Profile
            </Link>
            
            <Link 
              href="/dashboard" 
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors text-center"
            >
              📊 Dashboard
            </Link>
            
            <Link 
              href="/" 
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors text-center"
            >
              🏠 Home
            </Link>
            
            <Link 
              href="/router-test" 
              className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors text-center"
            >
              🔧 Router Test
            </Link>
          </div>
        </div>

        {/* Role-Specific Content */}
        {user.role === 'admin' && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">👑 Admin Dashboard</h3>
            <p className="text-gray-600 mb-4">Admin-specific content and controls:</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-red-50 p-4 rounded">
                <h4 className="font-medium text-red-900">User Management</h4>
                <p className="text-sm text-red-700">Manage all platform users</p>
              </div>
              <div className="bg-yellow-50 p-4 rounded">
                <h4 className="font-medium text-yellow-900">System Stats</h4>
                <p className="text-sm text-yellow-700">View platform statistics</p>
              </div>
              <div className="bg-green-50 p-4 rounded">
                <h4 className="font-medium text-green-900">Settings</h4>
                <p className="text-sm text-green-700">Configure system settings</p>
              </div>
            </div>
          </div>
        )}

        {user.role === 'patient' && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🏥 Patient Dashboard</h3>
            <p className="text-gray-600 mb-4">Patient-specific features:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link 
                href="/requests" 
                className="bg-blue-50 p-4 rounded hover:bg-blue-100 transition-colors"
              >
                <h4 className="font-medium text-blue-900">My Requests</h4>
                <p className="text-sm text-blue-700">View your healthcare requests</p>
              </Link>
              <Link 
                href="/requests/create" 
                className="bg-green-50 p-4 rounded hover:bg-green-100 transition-colors"
              >
                <h4 className="font-medium text-green-900">Create Request</h4>
                <p className="text-sm text-green-700">Request healthcare services</p>
              </Link>
              <Link 
                href="/nurses" 
                className="bg-purple-50 p-4 rounded hover:bg-purple-100 transition-colors"
              >
                <h4 className="font-medium text-purple-900">Find Nurses</h4>
                <p className="text-sm text-purple-700">Browse available nurses</p>
              </Link>
              <div className="bg-gray-50 p-4 rounded">
                <h4 className="font-medium text-gray-900">Health Records</h4>
                <p className="text-sm text-gray-700">Access your medical history</p>
              </div>
            </div>
          </div>
        )}

        {user.role === 'nurse' && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">👩‍⚕️ Nurse Dashboard</h3>
            <p className="text-gray-600 mb-4">Nurse-specific features:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link 
                href="/requests" 
                className="bg-green-50 p-4 rounded hover:bg-green-100 transition-colors"
              >
                <h4 className="font-medium text-green-900">My Requests</h4>
                <p className="text-sm text-green-700">View assigned requests</p>
              </Link>
              <div className="bg-blue-50 p-4 rounded">
                <h4 className="font-medium text-blue-900">Schedule</h4>
                <p className="text-sm text-blue-700">Manage your availability</p>
              </div>
              <div className="bg-purple-50 p-4 rounded">
                <h4 className="font-medium text-purple-900">Earnings</h4>
                <p className="text-sm text-purple-700">Track your earnings</p>
              </div>
              <div className="bg-yellow-50 p-4 rounded">
                <h4 className="font-medium text-yellow-900">Profile</h4>
                <p className="text-sm text-yellow-700">Update your professional info</p>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-900 mb-2">📋 Navigation Test Instructions</h3>
          <ol className="text-sm text-yellow-800 space-y-1">
            <li>1. <strong>Dropdown Test:</strong> Click your name/avatar in the top-right corner</li>
            <li>2. <strong>Profile Navigation:</strong> Click "Profile" in the dropdown menu</li>
            <li>3. <strong>Dashboard Navigation:</strong> Click "Dashboard" in the dropdown menu</li>
            <li>4. <strong>Direct Links:</strong> Use the navigation buttons above</li>
            <li>5. <strong>Role-Specific:</strong> Try role-specific links based on your user type</li>
            <li>6. <strong>Debug:</strong> Check the bottom-right corner for route information</li>
          </ol>
          
          <div className="mt-4 p-3 bg-yellow-100 rounded">
            <p className="text-sm text-yellow-800">
              <strong>Expected Behavior:</strong> All navigation should work smoothly. 
              If navigation fails, check the browser console for errors.
            </p>
          </div>
        </div>
      </div>
    </SimpleLayout>
  );
}
