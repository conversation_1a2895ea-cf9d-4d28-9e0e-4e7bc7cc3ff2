import { useRouter } from 'next/router';
import { useAuth } from '../lib/auth';
import { useState, useEffect } from 'react';

export default function IsolatedDashboard() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setLogs(prev => [...prev, logEntry]);
  };

  useEffect(() => {
    addLog('IsolatedDashboard component mounted');
    addLog(`Current route: ${router.pathname}`);
    addLog(`User: ${user?.email || 'Not logged in'}`);
  }, []);

  const handleNavigation = async (url: string) => {
    addLog(`Starting navigation to: ${url}`);
    setIsDropdownOpen(false);
    
    try {
      addLog('Calling router.push...');
      await router.push(url);
      addLog(`Successfully navigated to: ${url}`);
    } catch (error) {
      addLog(`Router.push failed: ${error}`);
      addLog('Trying window.location fallback...');
      window.location.href = url;
    }
  };

  const testWindowLocation = (url: string) => {
    addLog(`Testing window.location to: ${url}`);
    window.location.href = url;
  };

  if (!user) {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>Please Login</h1>
        <button 
          onClick={() => handleNavigation('/dev-auth')}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Go to Login
        </button>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f5f5f5'
    }}>
      {/* Simple Header */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px 20px', 
        borderBottom: '1px solid #ddd',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{ margin: 0, color: '#333' }}>Isolated Dashboard Test</h1>
        
        {/* Simple Dropdown */}
        <div style={{ position: 'relative' }}>
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            style={{
              padding: '8px 15px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <div style={{
              width: '30px',
              height: '30px',
              backgroundColor: '#0056b3',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '14px',
              fontWeight: 'bold'
            }}>
              {user.name.charAt(0).toUpperCase()}
            </div>
            {user.name}
            <span style={{ fontSize: '12px' }}>
              {isDropdownOpen ? '▲' : '▼'}
            </span>
          </button>
          
          {isDropdownOpen && (
            <div style={{
              position: 'absolute',
              top: '100%',
              right: 0,
              marginTop: '5px',
              backgroundColor: 'white',
              border: '1px solid #ddd',
              borderRadius: '5px',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              minWidth: '200px',
              zIndex: 1000
            }}>
              <button
                onClick={() => handleNavigation('/profile')}
                style={{
                  width: '100%',
                  padding: '12px 15px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  textAlign: 'left',
                  cursor: 'pointer',
                  borderBottom: '1px solid #eee'
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = '#f8f9fa'}
                onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
              >
                📄 Profile
              </button>
              
              <button
                onClick={() => handleNavigation('/dashboard')}
                style={{
                  width: '100%',
                  padding: '12px 15px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  textAlign: 'left',
                  cursor: 'pointer',
                  borderBottom: '1px solid #eee'
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = '#f8f9fa'}
                onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
              >
                📊 Dashboard
              </button>
              
              <button
                onClick={() => handleNavigation('/')}
                style={{
                  width: '100%',
                  padding: '12px 15px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  textAlign: 'left',
                  cursor: 'pointer',
                  borderBottom: '1px solid #eee'
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = '#f8f9fa'}
                onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
              >
                🏠 Home
              </button>
              
              <button
                onClick={() => {
                  setIsDropdownOpen(false);
                  logout();
                }}
                style={{
                  width: '100%',
                  padding: '12px 15px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  textAlign: 'left',
                  cursor: 'pointer',
                  color: '#dc3545'
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = '#f8f9fa'}
                onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
              >
                🚪 Logout
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div style={{ padding: '20px' }}>
        {/* User Info */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #ddd'
        }}>
          <h2 style={{ marginTop: 0, color: '#333' }}>Welcome, {user.name}!</h2>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            <div>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Role:</strong> {user.role}</p>
            </div>
            <div>
              <p><strong>Status:</strong> {user.status}</p>
              <p><strong>Current Route:</strong> {router.pathname}</p>
            </div>
          </div>
        </div>

        {/* Navigation Tests */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #ddd'
        }}>
          <h2 style={{ marginTop: 0, color: '#333' }}>Navigation Tests</h2>
          <p style={{ color: '#666', marginBottom: '15px' }}>
            Test navigation using different methods. Watch the logs below to see what happens.
          </p>
          
          <div style={{ marginBottom: '15px' }}>
            <h3 style={{ color: '#333', fontSize: '16px' }}>Router.push() Method:</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <button 
                onClick={() => handleNavigation('/profile')}
                style={{ 
                  padding: '10px 15px', 
                  backgroundColor: '#28a745', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                Profile (Router)
              </button>
              
              <button 
                onClick={() => handleNavigation('/dashboard')}
                style={{ 
                  padding: '10px 15px', 
                  backgroundColor: '#007bff', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                Dashboard (Router)
              </button>
              
              <button 
                onClick={() => handleNavigation('/')}
                style={{ 
                  padding: '10px 15px', 
                  backgroundColor: '#6f42c1', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                Home (Router)
              </button>
            </div>
          </div>
          
          <div>
            <h3 style={{ color: '#333', fontSize: '16px' }}>Window.location Method:</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <button 
                onClick={() => testWindowLocation('/profile')}
                style={{ 
                  padding: '10px 15px', 
                  backgroundColor: '#fd7e14', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                Profile (Window)
              </button>
              
              <button 
                onClick={() => testWindowLocation('/dashboard')}
                style={{ 
                  padding: '10px 15px', 
                  backgroundColor: '#fd7e14', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                Dashboard (Window)
              </button>
              
              <button 
                onClick={() => testWindowLocation('/')}
                style={{ 
                  padding: '10px 15px', 
                  backgroundColor: '#fd7e14', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                Home (Window)
              </button>
            </div>
          </div>
        </div>

        {/* Debug Logs */}
        <div style={{ 
          backgroundColor: '#1a1a1a', 
          color: '#00ff00', 
          padding: '20px', 
          borderRadius: '8px',
          fontFamily: 'monospace',
          fontSize: '12px'
        }}>
          <h2 style={{ color: '#00ff00', marginTop: 0 }}>Debug Logs:</h2>
          <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
            {logs.length === 0 ? (
              <p>No logs yet...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} style={{ marginBottom: '2px' }}>
                  {log}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Instructions */}
        <div style={{ 
          backgroundColor: '#fff3cd', 
          padding: '20px', 
          borderRadius: '8px',
          marginTop: '20px',
          border: '1px solid #ffeaa7'
        }}>
          <h2 style={{ color: '#856404', marginTop: 0 }}>🧪 Test Instructions:</h2>
          <ol style={{ color: '#856404' }}>
            <li>This is a completely isolated dashboard without the Layout component</li>
            <li>Try clicking the dropdown in the header and selecting "Profile"</li>
            <li>Try the "Profile (Router)" button below</li>
            <li>Compare with "Profile (Window)" button</li>
            <li>Watch the debug logs to see what's happening</li>
            <li>Check browser console (F12) for additional errors</li>
          </ol>
          
          <p style={{ color: '#856404', marginBottom: 0 }}>
            <strong>If this works:</strong> The issue is with the Layout component.<br/>
            <strong>If this doesn't work:</strong> The issue is with Next.js routing itself.
          </p>
        </div>
      </div>
    </div>
  );
}
