// API service layer for handling all backend requests
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Remove unused interface for now

class ApiService {
  private getAuthHeaders(): HeadersInit {
    const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch (e) {
        // If response is not JSON, use status text
        errorMessage = response.statusText || errorMessage;
      }
      throw new Error(errorMessage);
    }
    return response.json();
  }

  // Authentication endpoints
  async register(userData: any) {
    try {
      console.log('Registering user with data:', { ...userData, password: '[HIDDEN]' });
      console.log('API URL:', `${API_BASE_URL}/api/auth/register`);

      const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(userData),
      });

      console.log('Register response status:', response.status);
      return this.handleResponse(response);
    } catch (error) {
      console.error('Register error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Unable to connect to server. Please check if the backend is running.');
      }
      throw error;
    }
  }

  async login(credentials: { email: string; password: string }) {
    try {
      console.log('Logging in user:', credentials.email);
      console.log('API URL:', `${API_BASE_URL}/api/auth/login`);

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(credentials),
      });

      console.log('Login response status:', response.status);
      return this.handleResponse(response);
    } catch (error) {
      console.error('Login error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Unable to connect to server. Please check if the backend is running.');
      }
      throw error;
    }
  }

  async getProfile() {
    const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateProfile(profileData: any) {
    const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(profileData),
    });
    return this.handleResponse(response);
  }

  // Nurses endpoints
  async getNearbyNurses(params: {
    latitude: number;
    longitude: number;
    radius?: number;
    specializations?: string[];
  }) {
    const queryParams = new URLSearchParams({
      latitude: params.latitude.toString(),
      longitude: params.longitude.toString(),
      ...(params.radius && { radius: params.radius.toString() }),
      ...(params.specializations && { specializations: params.specializations.join(',') }),
    });

    const response = await fetch(`${API_BASE_URL}/api/nurses/nearby?${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async toggleNurseAvailability() {
    const response = await fetch(`${API_BASE_URL}/api/nurses/availability`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async verifyNurse(nurseId: string) {
    const response = await fetch(`${API_BASE_URL}/api/nurses/${nurseId}/verify`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Requests endpoints
  async createRequest(requestData: any) {
    const response = await fetch(`${API_BASE_URL}/api/requests`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(requestData),
    });
    return this.handleResponse(response);
  }

  async getRequests(status?: string) {
    const queryParams = status ? `?status=${status}` : '';
    const response = await fetch(`${API_BASE_URL}/api/requests${queryParams}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getRequestById(requestId: string) {
    const response = await fetch(`${API_BASE_URL}/api/requests/${requestId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async updateRequestStatus(requestId: string, status: string, cancellationReason?: string) {
    const response = await fetch(`${API_BASE_URL}/api/requests/${requestId}/status`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status, cancellationReason }),
    });
    return this.handleResponse(response);
  }

  async getDashboardStats() {
    const response = await fetch(`${API_BASE_URL}/api/requests/dashboard/stats`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Admin endpoints
  async getPendingNurses() {
    const response = await fetch(`${API_BASE_URL}/api/admin/pending-nurses`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getAdminStats() {
    const response = await fetch(`${API_BASE_URL}/api/admin/stats`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }
}

export const apiService = new ApiService();
export default apiService;
