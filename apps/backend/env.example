# Database Configuration
MONGODB_URI=mongodb://localhost:27017/nurse-platform

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-32-characters-long

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Email Configuration (Gmail example)
MAIL_HOST=smtp.gmail.com
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your-app-password

# Logging
LOG_LEVEL=debug

# OpenAI Configuration (for AI chat feature)
OPENAI_API_KEY=your-openai-api-key

# Optional: Redis for caching (if needed)
# REDIS_URL=redis://localhost:6379 